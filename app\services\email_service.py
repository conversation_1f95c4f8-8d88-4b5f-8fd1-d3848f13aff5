"""
Simple Email Service stub for production notifications.
"""

import logging
from typing import List, Optional, Dict, Any
from app.core.config import get_settings

# Setup logger
app_logger = logging.getLogger(__name__)


class EmailService:
    """Simple email service for production notifications."""
    
    def __init__(self):
        """Initialize email service with settings."""
        self.settings = get_settings()
        self.from_email = getattr(self.settings, 'from_email', '<EMAIL>')
        self.alert_recipients = getattr(self.settings, 'alert_email_recipients', '').split(',') if hasattr(self.settings, 'alert_email_recipients') else []
        
    def is_configured(self) -> bool:
        """Check if email service is properly configured."""
        return bool(self.from_email and self.alert_recipients)
    
    async def send_production_order_notification(
        self,
        order_data: Dict[str, Any],
        notification_type: str,
        recipients: Optional[List[str]] = None
    ) -> bool:
        """Send production order notification email."""
        try:
            # For now, just log the notification
            app_logger.info(f"Email notification (stub): {notification_type} for order {order_data.get('NUMERO_OFDA', 'Unknown')}")
            return True
        except Exception as e:
            app_logger.error(f"Error sending email notification: {e}")
            return False
    
    async def send_alert_email(
        self,
        subject: str,
        message: str,
        recipients: Optional[List[str]] = None
    ) -> bool:
        """Send alert email."""
        try:
            # For now, just log the alert
            app_logger.info(f"Email alert (stub): {subject}")
            return True
        except Exception as e:
            app_logger.error(f"Error sending alert email: {e}")
            return False
