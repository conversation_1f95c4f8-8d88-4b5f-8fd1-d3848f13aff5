<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Connexion - Suivi Production Excalibur ERP</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        position: relative;
        overflow: hidden;
      }

      body::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
      }

      .login-container {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 24px;
        box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12),
          0 16px 32px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.04);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        max-width: 420px;
        width: 100%;
        position: relative;
        z-index: 10;
        animation: slideUp 0.6s ease-out;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem 2.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .login-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%
        );
        pointer-events: none;
      }

      .login-header .logo-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .login-header h2 {
        margin: 0;
        font-weight: 600;
        font-size: 2rem;
        letter-spacing: -0.5px;
        position: relative;
        z-index: 2;
      }

      .login-header .subtitle {
        margin-top: 0.75rem;
        opacity: 0.95;
        font-size: 1rem;
        font-weight: 400;
        position: relative;
        z-index: 2;
      }

      .login-body {
        padding: 2rem;
      }

      .form-floating {
        margin-bottom: 1rem;
      }

      .form-control {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 1rem;
        font-weight: 400;
        background: rgba(255, 255, 255, 0.9);
      }

      .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
      }

      .form-floating > label {
        font-weight: 500;
        color: #6c757d;
        transition: all 0.3s ease;
      }

      .form-floating > .form-control:focus ~ label,
      .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #667eea;
        font-weight: 600;
      }

      .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        letter-spacing: 0.5px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        position: relative;
        overflow: hidden;
      }

      .btn-login::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn-login:hover::before {
        left: 100%;
      }

      .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }

      .btn-login:active {
        transform: translateY(-1px);
      }

      .alert {
        border-radius: 10px;
        border: none;
        margin-bottom: 1rem;
      }

      .company-info {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
        color: #6c757d;
        font-size: 0.85rem;
      }

      .security-notice {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        font-size: 0.8rem;
        color: #6c757d;
        text-align: center;
      }

      .default-credentials {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
        border: 1px solid rgba(25, 118, 210, 0.1);
        position: relative;
        overflow: hidden;
      }

      .default-credentials::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #1976d2, #7b1fa2);
      }

      .default-credentials h6 {
        color: #1976d2;
        margin-bottom: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .credential-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .credential-item:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(5px);
      }

      .credential-item:last-child {
        margin-bottom: 0;
      }

      .credential-label {
        font-weight: 600;
        color: #1976d2;
      }

      .credential-value {
        font-family: "Courier New", monospace;
        background: rgba(25, 118, 210, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.85rem;
      }

      .loading-spinner {
        display: none;
      }

      .btn-login.loading .loading-spinner {
        display: inline-block;
      }

      .btn-login.loading .btn-text {
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="login-header">
        <i class="fas fa-industry logo-icon"></i>
        <h2>Suivi Production</h2>
        <div class="subtitle">Excalibur ERP - Accès Sécurisé</div>
      </div>

      <div class="login-body">

        <!-- Error message placeholder -->
        <div
          id="error-message"
          class="alert alert-danger"
          style="display: none"
        >
          <i class="fas fa-exclamation-triangle"></i>
          <span id="error-text"></span>
        </div>

        <!-- Login form -->
        <form id="loginForm" method="post" action="/auth/login">
          <div class="form-floating">
            <input
              type="text"
              class="form-control"
              id="username"
              name="username"
              placeholder="Nom d'utilisateur"
              required
            />
            <label for="username">
              <i class="fas fa-user"></i> Nom d'utilisateur
            </label>
          </div>

          <div class="form-floating">
            <input
              type="password"
              class="form-control"
              id="password"
              name="password"
              placeholder="Mot de passe"
              required
            />
            <label for="password">
              <i class="fas fa-lock"></i> Mot de passe
            </label>
          </div>

          <button type="submit" class="btn btn-primary btn-login">
            <span class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span class="btn-text">
              <i class="fas fa-sign-in-alt"></i> Se connecter
            </span>
          </button>
        </form>

        <div class="security-notice">
          <i class="fas fa-shield-alt"></i>
          Accès sécurisé aux données privées de l'entreprise
        </div>

        <div class="company-info">
          <strong>Excalibur ERP</strong><br />
          Système de suivi de production<br />
          <small>Version 2.0.0</small>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      document
        .getElementById("loginForm")
        .addEventListener("submit", function (e) {
          const submitBtn = this.querySelector(".btn-login");
          const errorDiv = document.getElementById("error-message");

          // Show loading state
          submitBtn.classList.add("loading");
          submitBtn.disabled = true;

          // Hide previous errors
          errorDiv.style.display = "none";

          // The form will submit normally, but we show loading state
          // Error handling will be done server-side with redirects
        });

      // Auto-fill demo credentials (remove in production)
      document.addEventListener("DOMContentLoaded", function () {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get("demo") === "admin") {
          document.getElementById("username").value = "admin";
          document.getElementById("password").value = "admin123";
        } else if (urlParams.get("demo") === "user") {
          document.getElementById("username").value = "user";
          document.getElementById("password").value = "user123";
        }
      });

      // Handle login errors from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const error = urlParams.get("error");
      if (error) {
        const errorDiv = document.getElementById("error-message");
        const errorText = document.getElementById("error-text");

        switch (error) {
          case "invalid_credentials":
            errorText.textContent =
              "Nom d'utilisateur ou mot de passe incorrect.";
            break;
          case "inactive_user":
            errorText.textContent = "Compte utilisateur désactivé.";
            break;
          case "server_error":
            errorText.textContent = "Erreur serveur. Veuillez réessayer.";
            break;
          default:
            errorText.textContent = "Erreur de connexion. Veuillez réessayer.";
        }

        errorDiv.style.display = "block";
      }
    </script>
  </body>
</html>
