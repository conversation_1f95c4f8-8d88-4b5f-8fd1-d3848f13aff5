<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug Dashboard</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container mt-4">
      <h1>Dashboard Debug Test</h1>

      <!-- Test KPI Cards Container -->
      <div class="row mb-4">
        <div class="col-12">
          <h3>KPI Cards Test</h3>
          <div id="enCoursKpiCards" class="row">
            <!-- KPI cards will be populated here -->
          </div>
        </div>
      </div>

      <!-- Test Table Container -->
      <div class="row">
        <div class="col-12">
          <h3>Table Test</h3>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead id="enCoursHeader">
                <!-- Header will be populated here -->
              </thead>
              <tbody id="enCoursBody">
                <tr>
                  <td colspan="8" class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Debug Console -->
      <div class="row mt-4">
        <div class="col-12">
          <h3>Debug Console</h3>
          <div
            id="debugConsole"
            class="bg-dark text-light p-3"
            style="height: 200px; overflow-y: auto; font-family: monospace"
          >
            <!-- Debug messages will appear here -->
          </div>
        </div>
      </div>

      <!-- Test Buttons -->
      <div class="row mt-3">
        <div class="col-12">
          <button class="btn btn-primary" onclick="testKPIRendering()">
            Test KPI Rendering
          </button>
          <button class="btn btn-success" onclick="testTableRendering()">
            Test Table Rendering
          </button>
          <button class="btn btn-info" onclick="testAPICall()">
            Test API Call
          </button>
          <button class="btn btn-warning" onclick="clearDebug()">
            Clear Debug
          </button>
        </div>
      </div>
    </div>

    <script>
      // Debug console function
      function debugLog(message) {
        const console = document.getElementById("debugConsole");
        const timestamp = new Date().toLocaleTimeString();
        console.innerHTML += `[${timestamp}] ${message}\n`;
        console.scrollTop = console.scrollHeight;
      }

      // Test KPI rendering with mock data
      function testKPIRendering() {
        debugLog("🧪 Testing KPI rendering...");

        const mockData = [
          { STATUT: "C", Avancement_PROD: 0.75, Alerte_temps: false },
          { STATUT: "T", Avancement_PROD: 1.0, Alerte_temps: false },
          { STATUT: "C", Avancement_PROD: 0.45, Alerte_temps: true },
        ];

        const container = document.getElementById("enCoursKpiCards");
        if (!container) {
          debugLog("❌ KPI container not found!");
          return;
        }

        // Calculate KPIs
        const totalOrders = mockData.length;
        const activeOrders = mockData.filter(
          (item) => item.STATUT === "C"
        ).length;
        const completedOrders = mockData.filter(
          (item) => item.STATUT === "T"
        ).length;
        const avgProgress =
          mockData.reduce((sum, item) => sum + item.Avancement_PROD, 0) /
          mockData.length;
        const completionRate = (completedOrders / totalOrders) * 100;

        container.innerHTML = `
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${totalOrders}</h4>
                            <p>Total Commandes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${activeOrders}</h4>
                            <p>En Cours</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${completionRate.toFixed(1)}%</h4>
                            <p>Taux Completion</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${(avgProgress * 100).toFixed(1)}%</h4>
                            <p>Avancement Moyen</p>
                        </div>
                    </div>
                </div>
            `;

        debugLog("✅ KPI rendering completed successfully");
      }

      // Test table rendering with mock data
      function testTableRendering() {
        debugLog("🧪 Testing table rendering...");

        const mockData = [
          {
            NUMERO_OFDA: "OF001",
            PRODUIT: "Product A",
            DESIGNATION: "Test Product A",
            STATUT: "C",
            CLIENT: "Client 1",
            FAMILLE_TECHNIQUE: "Family A",
            Avancement_PROD: 0.75,
          },
          {
            NUMERO_OFDA: "OF002",
            PRODUIT: "Product B",
            DESIGNATION: "Test Product B",
            STATUT: "T",
            CLIENT: "Client 2",
            FAMILLE_TECHNIQUE: "Family B",
            Avancement_PROD: 1.0,
          },
        ];

        const headerElement = document.getElementById("enCoursHeader");
        const bodyElement = document.getElementById("enCoursBody");

        if (!headerElement || !bodyElement) {
          debugLog("❌ Table elements not found!");
          return;
        }

        // Render header
        headerElement.innerHTML = `
                <tr>
                    <th>N° OF</th>
                    <th>Produit</th>
                    <th>Désignation</th>
                    <th>Statut</th>
                    <th>Client</th>
                    <th>Famille</th>
                    <th>Avancement</th>
                    <th>Actions</th>
                </tr>
            `;

        // Render body
        const rowsHtml = mockData
          .map((row) => {
            const progressPercentage = Math.round(row.Avancement_PROD * 100);
            const statusClass = row.STATUT === "C" ? "primary" : "success";

            return `
                    <tr>
                        <td>${row.NUMERO_OFDA}</td>
                        <td>${row.PRODUIT}</td>
                        <td>${row.DESIGNATION}</td>
                        <td><span class="badge bg-${statusClass}">${row.STATUT}</span></td>
                        <td>${row.CLIENT}</td>
                        <td>${row.FAMILLE_TECHNIQUE}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-${statusClass}" 
                                     style="width: ${progressPercentage}%">
                                    ${progressPercentage}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
          })
          .join("");

        bodyElement.innerHTML = rowsHtml;
        debugLog("✅ Table rendering completed successfully");
      }

      // Test API call
      async function testAPICall() {
        debugLog("🧪 Testing API call...");

        try {
          const response = await fetch("/api/of/en_cours");
          debugLog(`📡 API Response status: ${response.status}`);

          if (response.ok) {
            const data = await response.json();
            debugLog(
              `📥 API Response structure: ${JSON.stringify(
                data,
                null,
                2
              ).substring(0, 500)}...`
            );

            if (data.success && data.data) {
              // Check if data has of_list structure
              if (data.data.of_list) {
                debugLog(
                  `✅ API call successful - ${data.data.of_list.length} records in of_list`
                );

                // Test rendering with actual data
                if (data.data.of_list.length > 0) {
                  debugLog("🎨 Testing rendering with real API data...");
                  testTableRenderingWithData(data.data.of_list.slice(0, 3)); // Show first 3 records
                  testKPIRenderingWithData(data.data.of_list);
                }
              } else if (Array.isArray(data.data)) {
                debugLog(
                  `✅ API call successful - ${data.data.length} records in data array`
                );
              } else {
                debugLog(`⚠️ Data structure: ${typeof data.data}`);
              }
            } else {
              debugLog(`⚠️ API call returned no data or failed`);
            }
          } else {
            debugLog(`❌ API call failed with status: ${response.status}`);
          }
        } catch (error) {
          debugLog(`❌ API call error: ${error.message}`);
        }
      }

      // Test rendering with real API data
      function testTableRenderingWithData(realData) {
        debugLog("🎨 Rendering table with real API data...");

        const headerElement = document.getElementById("enCoursHeader");
        const bodyElement = document.getElementById("enCoursBody");

        if (!headerElement || !bodyElement) {
          debugLog("❌ Table elements not found!");
          return;
        }

        // Render header
        headerElement.innerHTML = `
                <tr>
                    <th>N° OF</th>
                    <th>Produit</th>
                    <th>Statut</th>
                    <th>Client</th>
                    <th>Avancement</th>
                </tr>
            `;

        // Render body with real data
        const rowsHtml = realData
          .map((row) => {
            const progressPercentage = Math.round(
              (row.Avancement_PROD || 0) * 100
            );
            const statusClass =
              row.STATUT === "C"
                ? "primary"
                : row.STATUT === "T"
                ? "success"
                : "warning";

            return `
                    <tr>
                        <td>${row.NUMERO_OFDA || "N/A"}</td>
                        <td>${row.PRODUIT || "N/A"}</td>
                        <td><span class="badge bg-${statusClass}">${
              row.STATUT || "N/A"
            }</span></td>
                        <td>${row.CLIENT || "N/A"}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-${statusClass}"
                                     style="width: ${progressPercentage}%">
                                    ${progressPercentage}%
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
          })
          .join("");

        bodyElement.innerHTML = rowsHtml;
        debugLog("✅ Real data table rendering completed");
      }

      // Test KPI rendering with real API data
      function testKPIRenderingWithData(realData) {
        debugLog("📊 Rendering KPIs with real API data...");

        const container = document.getElementById("enCoursKpiCards");
        if (!container) {
          debugLog("❌ KPI container not found!");
          return;
        }

        // Calculate real KPIs
        const totalOrders = realData.length;
        const activeOrders = realData.filter(
          (item) => item.STATUT === "C"
        ).length;
        const completedOrders = realData.filter(
          (item) => item.STATUT === "T"
        ).length;
        const avgProgress =
          realData.reduce((sum, item) => sum + (item.Avancement_PROD || 0), 0) /
          realData.length;
        const completionRate =
          totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

        container.innerHTML = `
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4>${totalOrders}</h4>
                            <p>Total Commandes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4>${activeOrders}</h4>
                            <p>En Cours</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4>${completionRate.toFixed(1)}%</h4>
                            <p>Taux Completion</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4>${(avgProgress * 100).toFixed(1)}%</h4>
                            <p>Avancement Moyen</p>
                        </div>
                    </div>
                </div>
            `;

        debugLog("✅ Real data KPI rendering completed");
      }

      // Clear debug console
      function clearDebug() {
        document.getElementById("debugConsole").innerHTML = "";
      }

      // Initialize debug
      document.addEventListener("DOMContentLoaded", function () {
        debugLog("🚀 Debug dashboard initialized");
      });
    </script>

    <!-- Font Awesome for icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
  </body>
</html>
