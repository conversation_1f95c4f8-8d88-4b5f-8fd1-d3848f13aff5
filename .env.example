# FastAPI Application Configuration
APP_NAME=Suivi Production - Excalibur ERP
APP_DESCRIPTION=API pour le suivi en temps réel des indicateurs de production
APP_VERSION=2.0.0
DEBUG=false

# Database Configuration
DB_UID=your_database_username
DB_PWD=your_database_password
DB_HOST=your_database_host
DB_SERVER_NAME=your_server_name
DB_DATABASE_NAME=your_database_name

# Server Configuration
HOST=localhost
PORT=8000
RELOAD=true

# Logging Configuration
LOG_LEVEL=info

# Authentication Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=480

# Default User Credentials (CHANGE IN PRODUCTION!)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_USER_USERNAME=user
DEFAULT_USER_PASSWORD=user123

# Email/SMTP Configuration
SMTP_HOST=smtp.your-domain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>
ALERT_EMAIL_RECIPIENTS="bennjima.samir.22.com"

# Alert Configuration
ENABLE_ALERTS=true
ALERT_CHECK_INTERVAL=1800

# Production Notification Configuration
ENABLE_PRODUCTION_NOTIFICATIONS=true
DAILY_SUMMARY_TIME=08:00
OVERDUE_CHECK_INTERVAL=1800
URGENT_THRESHOLD_DAYS=2

# Monitoring Configuration
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2.0
