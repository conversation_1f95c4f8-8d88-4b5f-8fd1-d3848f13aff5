# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.emka-env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
*.log
app_health.log
backups/
temp/
tmp/

# Database
*.db
*.sqlite
*.sqlite3

# Environment variables
.env.local
.env.production

# Temporary files
*.tmp
*.bak
*.backup

# Test and validation files
test_*.py
validate_*.py
*_test.py
*_validation.py
*.test.html
*.debug.html
api_test_report_*.json
chart_debug.html
chart_test.html

# Documentation files (except main documentation)
*_IMPLEMENTATION*.md
*_README*.md
*_SUMMARY*.md
*_DOCUMENTATION*.md
ENVIRONMENT_FIX.md
RESUME_*.md
manual_test_examples.md
!COMPLETE_DOCUMENTATION.md
