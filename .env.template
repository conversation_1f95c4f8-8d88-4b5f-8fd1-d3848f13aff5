# Database Configuration for Excalibur ERP
# Copy this file to .env and update with your actual credentials

# Database Connection Parameters
DB_UID=gpao
DB_PWD=flat
DB_HOST=*************:2638
DB_SERVER_NAME=excalib
DB_DATABASE_NAME=excalib

# Optional: Application Configuration
APP_HOST=localhost
APP_PORT=8000
APP_RELOAD=true

# Optional: Logging Configuration
LOG_LEVEL=info

# Email/SMTP Configuration for Alerts and Production Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Alert Configuration
ENABLE_ALERTS=true
ALERT_CHECK_INTERVAL=1800

# Production Notification Settings
ENABLE_PRODUCTION_NOTIFICATIONS=true
DAILY_SUMMARY_TIME=08:00
OVERDUE_CHECK_INTERVAL=1800
URGENT_THRESHOLD_DAYS=2
COMPLETION_NOTIFICATION_ENABLED=true

# Monitoring Configuration
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=2.0
