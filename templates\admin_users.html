<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Administration - Gestion des Utilisateurs</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand" href="/unified">
          <i class="fas fa-industry me-2"></i>
          Excalibur ERP - Administration
        </a>
        <div class="navbar-nav ms-auto">
          <a class="nav-link" href="/unified">
            <i class="fas fa-arrow-left me-1"></i>
            Retour au Dashboard
          </a>
          <a class="nav-link" href="/auth/logout">
            <i class="fas fa-sign-out-alt me-1"></i>
            Déconnexion
          </a>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users me-2"></i>Gestion des Utilisateurs</h1>
            <button
              class="btn btn-primary"
              data-bs-toggle="modal"
              data-bs-target="#addUserModal"
            >
              <i class="fas fa-plus me-2"></i>Ajouter Utilisateur
            </button>
          </div>

          <!-- Search and Filter -->
          <div class="card mb-3">
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      type="text"
                      class="form-control"
                      id="searchUsers"
                      placeholder="Rechercher par nom d'utilisateur, nom complet ou email..."
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <select class="form-select" id="filterRole">
                    <option value="">Tous les rôles</option>
                    <option value="admin">Administrateur</option>
                    <option value="user">Utilisateur</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <button
                    class="btn btn-outline-secondary"
                    onclick="clearFilters()"
                  >
                    <i class="fas fa-times me-1"></i>Effacer filtres
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Users Table -->
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Liste des Utilisateurs</h5>
              <span class="badge bg-primary" id="userCount"
                >{{ users|length }} utilisateur(s)</span
              >
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Nom d'utilisateur</th>
                      <th>Nom complet</th>
                      <th>Email</th>
                      <th>Rôle</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for username, user in users.items() %}
                    <tr>
                      <td>
                        <i
                          class="fas fa-{{ 'crown' if user.role == 'admin' else ('hard-hat' if user.role == 'chef' else 'user') }} me-2"
                        ></i>
                        {{ user.username }}
                      </td>
                      <td>{{ user.full_name }}</td>
                      <td>{{ user.email }}</td>
                      <td>
                        <span
                          class="badge bg-{{ 'danger' if user.role == 'admin' else ('warning' if user.role == 'chef' else 'primary') }}"
                        >
                          {% if user.role == 'admin' %} Administrateur {% elif
                          user.role == 'chef' %} Chef de Production {% else %}
                          Utilisateur {% endif %}
                        </span>
                      </td>

                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-info"
                            onclick="viewUser('{{ user.username }}')"
                            title="Voir détails"
                          >
                            <i class="fas fa-eye"></i>
                          </button>
                          <button
                            class="btn btn-outline-primary"
                            onclick="editUser('{{ user.username }}')"
                            title="Modifier"
                          >
                            <i class="fas fa-edit"></i>
                          </button>
                          <button
                            class="btn btn-outline-warning"
                            onclick="changePassword('{{ user.username }}')"
                            title="Changer le mot de passe"
                          >
                            <i class="fas fa-key"></i>
                          </button>

                          {% if user.username != 'admin' %}
                          <button
                            class="btn btn-outline-danger"
                            onclick="deleteUser('{{ user.username }}')"
                            title="Supprimer"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                          {% endif %}
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Ajouter un Utilisateur</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <form id="addUserForm" method="post" action="/admin/users/create">
            <div class="modal-body">
              <div class="mb-3">
                <label for="username" class="form-label"
                  >Nom d'utilisateur *</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="username"
                  name="username"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="password" class="form-label">Mot de passe *</label>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  name="password"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="full_name" class="form-label">Nom complet</label>
                <input
                  type="text"
                  class="form-control"
                  id="full_name"
                  name="full_name"
                />
              </div>
              <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  name="email"
                />
              </div>
              <div class="mb-3">
                <label for="role" class="form-label">Rôle</label>
                <select class="form-select" id="role" name="role">
                  <option value="user">Utilisateur</option>
                  <option value="chef">Chef de Production</option>
                  <option value="admin">Administrateur</option>
                </select>
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Annuler
              </button>
              <button type="submit" class="btn btn-primary">
                Créer Utilisateur
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- View User Modal -->
    <div class="modal fade" id="viewUserModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Détails de l'Utilisateur</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <strong>Nom d'utilisateur:</strong>
                <p id="viewUsername" class="text-muted">-</p>
              </div>
              <div class="col-md-6">
                <strong>Nom complet:</strong>
                <p id="viewFullName" class="text-muted">-</p>
              </div>
              <div class="col-md-6">
                <strong>Email:</strong>
                <p id="viewEmail" class="text-muted">-</p>
              </div>
              <div class="col-md-6">
                <strong>Rôle:</strong>
                <p id="viewRole" class="text-muted">-</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Modifier l'Utilisateur</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <form id="editUserForm">
            <div class="modal-body">
              <input type="hidden" id="editUsername" name="username" />
              <div class="mb-3">
                <label for="editFullName" class="form-label"
                  >Nom complet *</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="editFullName"
                  name="full_name"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="editEmail" class="form-label">Email *</label>
                <input
                  type="email"
                  class="form-control"
                  id="editEmail"
                  name="email"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="editRole" class="form-label">Rôle</label>
                <select class="form-select" id="editRole" name="role">
                  <option value="user">Utilisateur</option>
                  <option value="chef">Chef de Production</option>
                  <option value="admin">Administrateur</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="editIsActive" class="form-label">Statut</label>
                <select class="form-select" id="editIsActive" name="is_active">
                  <option value="true">Actif</option>
                  <option value="false">Inactif</option>
                </select>
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Annuler
              </button>
              <button type="submit" class="btn btn-primary">Sauvegarder</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmer la Suppression</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <strong>Attention!</strong> Cette action est irréversible.
            </div>
            <p>
              Êtes-vous sûr de vouloir supprimer l'utilisateur
              <strong id="deleteUserName">-</strong> ?
            </p>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Annuler
            </button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
              Supprimer
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Changer le Mot de Passe</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <form id="changePasswordForm">
            <div class="modal-body">
              <div class="mb-3">
                <label for="newPassword" class="form-label"
                  >Nouveau mot de passe</label
                >
                <input
                  type="password"
                  class="form-control"
                  id="newPassword"
                  name="new_password"
                  required
                  minlength="4"
                />
                <div class="form-text">Minimum 4 caractères</div>
              </div>
              <div class="mb-3">
                <label for="confirmPassword" class="form-label"
                  >Confirmer le mot de passe</label
                >
                <input
                  type="password"
                  class="form-control"
                  id="confirmPassword"
                  required
                />
              </div>
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-secondary"
                data-bs-dismiss="modal"
              >
                Annuler
              </button>
              <button type="submit" class="btn btn-warning">Changer</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      let currentUsername = "";

      // View User Details
      function viewUser(username) {
        fetch(`/admin/users/${username}`)
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              const user = data.data;
              document.getElementById("viewUsername").textContent =
                user.username;
              document.getElementById("viewFullName").textContent =
                user.full_name;
              document.getElementById("viewEmail").textContent = user.email;
              document.getElementById("viewRole").textContent =
                user.role === "admin" ? "Administrateur" : "Utilisateur";

              new bootstrap.Modal(
                document.getElementById("viewUserModal")
              ).show();
            } else {
              showAlert("danger", data.message);
            }
          })
          .catch((error) => {
            showAlert("danger", "Erreur: " + error.message);
          });
      }

      // Edit User
      function editUser(username) {
        fetch(`/admin/users/${username}`)
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              const user = data.data;
              document.getElementById("editUsername").value = user.username;
              document.getElementById("editFullName").value = user.full_name;
              document.getElementById("editEmail").value = user.email;
              document.getElementById("editRole").value = user.role || "user";
              document.getElementById("editIsActive").value = user.is_active
                ? "true"
                : "false";

              document
                .getElementById("editUserModal")
                .querySelector(
                  ".modal-title"
                ).textContent = `Modifier - ${user.username}`;

              new bootstrap.Modal(
                document.getElementById("editUserModal")
              ).show();
            } else {
              showAlert("danger", data.message);
            }
          })
          .catch((error) => {
            showAlert("danger", "Erreur: " + error.message);
          });
      }

      function changePassword(username) {
        currentUsername = username;
        document
          .getElementById("changePasswordModal")
          .querySelector(
            ".modal-title"
          ).textContent = `Changer le mot de passe - ${username}`;
        document.getElementById("newPassword").value = "";
        document.getElementById("confirmPassword").value = "";
        new bootstrap.Modal(
          document.getElementById("changePasswordModal")
        ).show();
      }

      // Delete User
      function deleteUser(username) {
        currentUsername = username;
        document.getElementById("deleteUserName").textContent = username;
        new bootstrap.Modal(document.getElementById("deleteUserModal")).show();
      }

      // Show Alert
      function showAlert(type, message) {
        const alertDiv = document.createElement("div");
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document
          .querySelector(".container")
          .insertBefore(alertDiv, document.querySelector(".row"));

        setTimeout(() => {
          alertDiv.remove();
        }, 5000);
      }

      // Search and Filter functionality
      function filterUsers() {
        const searchTerm = document
          .getElementById("searchUsers")
          .value.toLowerCase();
        const roleFilter = document.getElementById("filterRole").value;

        const rows = document.querySelectorAll("tbody tr");
        let visibleCount = 0;

        rows.forEach((row) => {
          const username = row.cells[0].textContent.toLowerCase();
          const fullName = row.cells[1].textContent.toLowerCase();
          const email = row.cells[2].textContent.toLowerCase();
          const role = row.cells[3].textContent.toLowerCase();

          const matchesSearch =
            !searchTerm ||
            username.includes(searchTerm) ||
            fullName.includes(searchTerm) ||
            email.includes(searchTerm);

          const matchesRole =
            !roleFilter ||
            (roleFilter === "admin" && role.includes("administrateur")) ||
            (roleFilter === "user" && role.includes("utilisateur"));

          if (matchesSearch && matchesRole) {
            row.style.display = "";
            visibleCount++;
          } else {
            row.style.display = "none";
          }
        });

        document.getElementById(
          "userCount"
        ).textContent = `${visibleCount} utilisateur(s)`;
      }

      // Clear all filters
      function clearFilters() {
        document.getElementById("searchUsers").value = "";
        document.getElementById("filterRole").value = "";
        filterUsers();
      }

      // Add event listeners for search and filter
      document.addEventListener("DOMContentLoaded", function () {
        document
          .getElementById("searchUsers")
          .addEventListener("input", filterUsers);
        document
          .getElementById("filterRole")
          .addEventListener("change", filterUsers);
      });

      // Handle form submissions
      document
        .getElementById("addUserForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const formData = new FormData(this);

          fetch("/admin/users/create", {
            method: "POST",
            body: formData,
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                bootstrap.Modal.getInstance(
                  document.getElementById("addUserModal")
                ).hide();
                showAlert("success", data.message);
                setTimeout(() => location.reload(), 1000);
              } else {
                showAlert("danger", data.message);
              }
            })
            .catch((error) => {
              showAlert("danger", "Erreur: " + error.message);
            });
        });

      // Edit User Form
      document
        .getElementById("editUserForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const formData = new FormData();
          formData.append(
            "full_name",
            document.getElementById("editFullName").value
          );
          formData.append("email", document.getElementById("editEmail").value);
          formData.append("role", document.getElementById("editRole").value);
          formData.append(
            "is_active",
            document.getElementById("editIsActive").value === "true"
          );

          const username = document.getElementById("editUsername").value;

          fetch(`/admin/users/${username}`, {
            method: "PUT",
            body: formData,
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                bootstrap.Modal.getInstance(
                  document.getElementById("editUserModal")
                ).hide();
                showAlert("success", data.message);
                setTimeout(() => location.reload(), 1000);
              } else {
                showAlert("danger", data.message);
              }
            })
            .catch((error) => {
              showAlert("danger", "Erreur: " + error.message);
            });
        });

      // Change Password Form
      document
        .getElementById("changePasswordForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          const newPassword = document.getElementById("newPassword").value;
          const confirmPassword =
            document.getElementById("confirmPassword").value;

          if (newPassword !== confirmPassword) {
            showAlert("danger", "Les mots de passe ne correspondent pas");
            return;
          }

          if (newPassword.length < 4) {
            showAlert(
              "danger",
              "Le mot de passe doit contenir au moins 4 caractères"
            );
            return;
          }

          const formData = new FormData();
          formData.append("new_password", newPassword);

          fetch(`/admin/users/${currentUsername}/password`, {
            method: "POST",
            body: formData,
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                bootstrap.Modal.getInstance(
                  document.getElementById("changePasswordModal")
                ).hide();
                showAlert("success", data.message);
              } else {
                showAlert("danger", data.message);
              }
            })
            .catch((error) => {
              showAlert("danger", "Erreur: " + error.message);
            });
        });

      // Delete User Confirmation
      document
        .getElementById("confirmDeleteBtn")
        .addEventListener("click", function () {
          fetch(`/admin/users/${currentUsername}`, {
            method: "DELETE",
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                bootstrap.Modal.getInstance(
                  document.getElementById("deleteUserModal")
                ).hide();
                showAlert("success", data.message);
                setTimeout(() => location.reload(), 1000);
              } else {
                showAlert("danger", data.message);
              }
            })
            .catch((error) => {
              showAlert("danger", "Erreur: " + error.message);
            });
        });
    </script>
  </body>
</html>
