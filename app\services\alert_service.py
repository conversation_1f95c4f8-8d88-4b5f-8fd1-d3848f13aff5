"""
Simple Alert Service stub for manual alerts.
"""

import logging
from typing import Optional
from datetime import datetime

# Setup logger
app_logger = logging.getLogger(__name__)


class AlertService:
    """Simple alert service for creating and managing alerts."""
    
    def __init__(self):
        """Initialize alert service."""
        self.alerts = []
    
    async def create_manual_alert(
        self,
        title: str,
        message: str,
        severity: str = "medium",
        alert_type: str = "general",
        send_email: bool = True
    ) -> bool:
        """Create a manual alert."""
        try:
            alert = {
                "title": title,
                "message": message,
                "severity": severity,
                "alert_type": alert_type,
                "timestamp": datetime.now(),
                "email_sent": send_email
            }
            
            self.alerts.append(alert)
            
            # For now, just log the alert
            app_logger.info(f"Manual alert created (stub): {title} - {severity}")
            
            if send_email:
                # Simulate email sending
                app_logger.info(f"Alert email sent (stub): {title}")
            
            return True
            
        except Exception as e:
            app_logger.error(f"Error creating manual alert: {e}")
            return False


# Global instance
alert_service = AlertService()
